import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/folders/view/folder_page.dart';
import 'package:paperless_mobile/features/linked_documents/view/linked_document_widget.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class ProjectDetailPage extends StatefulWidget {
  ProjectDetailPage({super.key, required this.project});
  Project project;

  @override
  State<ProjectDetailPage> createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends State<ProjectDetailPage> {
  final TextEditingController _projectNameController = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _projectNameController.text = widget.project.name;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _projectNameController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DocumentUploadCubit documentUploadCubit =
        context.read<DocumentUploadCubit>();
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: Row(
          children: [
            Text(
              widget.project.name,
              style: const TextStyle(color: AppColor.primary),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        actions: [
          IconButton(
              onPressed: () async {
                showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                          surfaceTintColor: Colors.transparent,
                          title: Text(
                            S.of(context)!.confirmDeleteProject,
                            style: AppTextStyles.textStyleBold20,
                          ),
                          content: Text(
                            S
                                .of(context)!
                                .thisActionWillPermanentlyDeleteThisProjectAndCannotBeUndone,
                            style: AppTextStyles.textStyle14,
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: Text(
                                S.of(context)!.cancel,
                                style: const TextStyle(
                                    color: AppColor.black_333333),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: () async {
                                Navigator.pop(context);
                                context.pop();

                                await documentUploadCubit.deleteProject(
                                    widget.project.id.toString());
                                await documentUploadCubit.getAllProject();
                              },
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColor.red_FA0A02),
                              child: Text(
                                S.of(context)!.delete,
                                style: const TextStyle(color: AppColor.white),
                              ),
                            ),
                          ],
                        ));
              },
              icon: SvgPicture.asset('assets/svgs/trash.svg')),
          const Gap(8)
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
        child: Column(
          children: [
            Row(
              children: [
                Text(S.of(context)!.projectName),
                const Text(
                  '*',
                  style: TextStyle(color: Colors.red),
                )
              ],
            ),
            const Gap(8),
            SizedBox(
              height: 48,
              child: TextField(
                controller: _projectNameController,
                decoration: InputDecoration(
                  fillColor: AppColor.white,
                  filled: true,
                  hintText: S.of(context)!.searchDepartment,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  // suffixIcon: const Icon(Icons.search),
                  hintStyle: const TextStyle(
                    color: AppColor.grey_909090,
                  ),
                ),
              ),
            ),
            Expanded(
                child: LinkedDocumentsWidget(
                    unlinkType: UnlinkType.project,
                    isShowUnlink: true,
                    documentUploadCubit: documentUploadCubit))
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
        child: GestureDetector(
          onTap: () async {
            Navigator.pop(context);
            await documentUploadCubit.updateProject(
                widget.project.id.toString(),
                _projectNameController.text.trim());
            documentUploadCubit.getAllProject();
          },
          child: Container(
            alignment: Alignment.center,
            height: 48,
            decoration: BoxDecoration(
                color: AppColor.primary,
                borderRadius: BorderRadius.circular(10)),
            child: Text(
              S.of(context)!.save,
              style: AppTextStyles.textStyle14
                  .copyWith(color: AppColor.white, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }
}
