import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_mobile/core/bloc/connectivity_cubit.dart';
import 'package:paperless_mobile/core/extensions/document_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/documents/view/widgets/adaptive_documents_view.dart';
import 'package:paperless_mobile/features/documents/view/widgets/selection/view_type_selection_widget.dart';
import 'package:paperless_mobile/features/linked_documents/cubit/linked_documents_cubit.dart';
import 'package:paperless_mobile/features/paged_document_view/view/document_paging_view_mixin.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

class LinkedDocumentsWidget extends StatefulWidget {
  const LinkedDocumentsWidget({super.key, this.isShowUnlink = false});

  final bool isShowUnlink;

  @override
  State<LinkedDocumentsWidget> createState() => _LinkedDocumentsWidgetState();
}

class _LinkedDocumentsWidgetState extends State<LinkedDocumentsWidget>
    with DocumentPagingViewMixin<LinkedDocumentsWidget, LinkedDocumentsCubit> {
  @override
  final pagingScrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(S.of(context)!.linkedDocuments,
                style: AppTextStyles.textStyleAppBar),
            BlocBuilder<LinkedDocumentsCubit, LinkedDocumentsState>(
              builder: (context, state) {
                return ViewTypeSelectionWidget(
                  viewType: state.viewType,
                  onChanged: context.read<LinkedDocumentsCubit>().setViewType,
                );
              },
            ),
          ],
        ),
        Expanded(
          child: BlocBuilder<LinkedDocumentsCubit, LinkedDocumentsState>(
            builder: (context, state) {
              return BlocBuilder<ConnectivityCubit, ConnectivityState>(
                builder: (context, connectivity) {
                  return CustomScrollView(
                    controller: pagingScrollController,
                    slivers: [
                      SliverAdaptiveDocumentsView(
                        height: 116,
                        width: 84,
                        padding: 156,
                        isShowUnlink: widget.isShowUnlink,
                        viewType: state.viewType,
                        documents: state.documents,
                        hasInternetConnection: connectivity.isConnected,
                        isLabelClickable: false,
                        isLoading: state.isLoading,
                        hasLoaded: state.hasLoaded,
                        onTap: (document) {
                          DocumentDetailsRoute(
                            title: document.title,
                            id: document.id,
                            isLabelClickable: false,
                            thumbnailUrl: document.buildThumbnailUrl(context),
                          ).push(context);
                        },
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
