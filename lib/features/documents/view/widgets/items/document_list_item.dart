import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/features/documents/view/widgets/date_and_document_type_widget.dart';
import 'package:paperless_mobile/features/documents/view/widgets/document_preview.dart';
import 'package:paperless_mobile/features/documents/view/widgets/items/document_item.dart';
import 'package:paperless_mobile/features/labels/tags/view/widgets/tags_widget.dart';
import 'package:provider/provider.dart';

class DocumentListItem extends DocumentItem {
  static const _a4AspectRatio = 1 / 1.4142;

  final Color? backgroundColor;
  const DocumentListItem(
      {super.key,
      this.backgroundColor,
      required super.document,
      required super.isSelected,
      required super.isSelectionActive,
      required super.isLabelClickable,
      super.onCorrespondentSelected,
      super.onDocumentTypeSelected,
      super.onSelected,
      super.onStoragePathSelected,
      super.onTagSelected,
      super.onTap,
      super.enableHeroAnimation = true,
      this.isMedium = true,
      this.padding = 0,
      this.height,
      this.isShowUnlink = false,
      this.width,
      this.onUnlink});

  final double? width;
  final double? height;
  final double padding;
  final bool isMedium;
  final bool isShowUnlink;
  final void Function(DocumentModel)? onUnlink;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final labelRepository = context.watch<LabelRepository>();
    return Container(
      color: isSelected ? AppColor.primary : null,
      child: ListTile(
        selectedColor: AppColor.white,
        minVerticalPadding: 0,
        dense: true,
        selected: isSelected,
        onTap: () => _onTap(),
        selectedTileColor: Theme.of(context).colorScheme.inversePrimary,
        onLongPress: onSelected != null ? () => onSelected!(document) : null,
        title: Row(
          children: [
            GestureDetector(
              child: DocumentPreview(
                height: height,
                width: width,
                documentId: document.id,
                title: document.title,
                fit: BoxFit.cover,
                alignment: Alignment.topCenter,
                enableHero: enableHeroAnimation,
              ),
            ),
            const Gap(24),
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: size.width - padding - (isShowUnlink ? 42 : 0),
                      child: Text(
                        document.title.isEmpty ? '-' : document.title,
                        style: TextStyle(
                            fontSize: isMedium ? 16 : 13,
                            fontWeight:
                                isMedium ? FontWeight.w600 : FontWeight.w500),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                    if (isMedium)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/svgs/calendar.svg',
                              color: isSelected ? AppColor.white : null,
                            ),
                            DateAndDocumentTypeLabelWidget(
                              document: document,
                              onDocumentTypeSelected: onDocumentTypeSelected,
                            ),
                          ],
                        ),
                      ),
                    if (isMedium)
                      AbsorbPointer(
                        absorbing: isSelectionActive,
                        child: TagsWidget(
                          isClickable: isLabelClickable,
                          tags: document.tags
                              .where((e) => labelRepository.tags.containsKey(e))
                              .map((e) => labelRepository.tags[e]!)
                              .toList(),
                          onTagSelected: (id) => onTagSelected?.call(id),
                        ),
                      ),
                  ],
                ),
                if (isShowUnlink)
                  GestureDetector(
                    onTap: () {
                      onUnlink?.call(document);
                    },
                    child: Container(
                        color: Colors.transparent, child: const Text('Unlink')),
                  )
              ],
            ),
          ],
        ),
        subtitle: const SizedBox(),
        isThreeLine: document.tags.isNotEmpty,
        contentPadding: const EdgeInsets.all(8.0),
      ),
    );
  }

  void _onTap() {
    if (isSelectionActive || isSelected) {
      onSelected?.call(document);
    } else {
      onTap?.call(document);
    }
  }
}
